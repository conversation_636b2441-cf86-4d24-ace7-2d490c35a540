#include "LicenseManager.h"
#include "HardwareFingerprint.h"
#include <QNetworkRequest>
#include <QUrlQuery>
#include <QJsonParseError>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>
#include <QRandomGenerator>
#include <QRegularExpression>

// 服务器配置（增强版）
const QString LicenseManager::SERVER_URL = "https://api.shifang.co";
const QString LicenseManager::ACTIVATION_ENDPOINT = "/license/activate";
const QString LicenseManager::VERIFICATION_ENDPOINT = "/license/verify";
const QString LicenseManager::DEACTIVATION_ENDPOINT = "/license/deactivate";

// 调试模式配置（增强版）
const QStringList LicenseManager::DEBUG_VALID_CODES = {
    "TEST-1234-5678-ABCD",
    "DEBUG-2024-0629-DEMO",
    "DEV-AUTOBIT-MODEL-CONV",
    "E76G-JEQR-EQRA-T7ZW",
    "YXPERM-TEST-CODE-001",  // 仿YXPermission测试码
    "ENHANCED-AUTOBIT-001"   // 增强版测试码
};
const QString LicenseManager::DEBUG_DEVICE_PREFIX = "DEBUG-DEVICE-";

// 新增常量（仿YXPermission）
const QString LicenseManager::ENCRYPTION_SALT = "AutobitEnhanced2024_YXStyle";
const QString LicenseManager::BINDING_SALT = "DeviceBinding_245ba9b4-a074-499c-974c-5377b15c699f";
const QString LicenseManager::PRODUCT_ID_DEFAULT = "40201";

LicenseManager::LicenseManager(QObject *parent)
    : QObject(parent)
    , networkManager(new QNetworkAccessManager(this))
    , debugMode(false)
    , verificationMode(LocalAndCloud)  // 默认本地+云端验证
    , currentProductId(PRODUCT_ID_DEFAULT)
{
    // 初始化设置存储
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QDir().mkpath(configPath);
    settings = new QSettings(configPath + "/license.conf", QSettings::IniFormat, this);

    qDebug() << "[许可证管理器] 初始化完成（增强版），配置路径:" << configPath;
    qDebug() << "[许可证管理器] 默认验证模式: LocalAndCloud";
    qDebug() << "[许可证管理器] 默认产品ID:" << currentProductId;
}

void LicenseManager::setDebugMode(bool enabled)
{
    debugMode = enabled;
    qDebug() << "[许可证管理器] 调试模式:" << (enabled ? "启用" : "禁用");
}

void LicenseManager::setVerificationMode(VerificationMode mode)
{
    verificationMode = mode;
    QString modeStr;
    switch (mode) {
        case LocalOnly: modeStr = "仅本地验证"; break;
        case CloudOnly: modeStr = "仅云端验证"; break;
        case LocalAndCloud: modeStr = "本地+云端验证"; break;
        case Adaptive: modeStr = "自适应验证"; break;
    }
    qDebug() << "[许可证管理器] 验证模式设置为:" << modeStr;
}

LicenseManager::LicenseStatus LicenseManager::checkLicenseStatus(bool forceCloudCheck)
{
    if (debugMode) {
        // 调试模式下总是返回有效状态
        qDebug() << "[许可证管理器] 调试模式：返回有效状态";
        return Valid;
    }

    QJsonObject license = loadLicense();
    if (license.isEmpty()) {
        qDebug() << "[许可证管理器] 未找到本地许可证";
        return NotActivated;
    }

    // 本地验证
    bool localValid = verifyLicenseLocal(license);
    if (!localValid) {
        qDebug() << "[许可证管理器] 本地验证失败";
        return Invalid;
    }

    // 根据验证模式决定是否需要云端验证
    bool needCloudCheck = forceCloudCheck ||
                         verificationMode == CloudOnly ||
                         verificationMode == LocalAndCloud ||
                         (verificationMode == Adaptive && shouldPerformCloudCheck());

    if (needCloudCheck) {
        qDebug() << "[许可证管理器] 执行云端验证";
        ActivationResult cloudResult = verifyLicenseCloud(currentProductId);
        if (cloudResult != Success) {
            qDebug() << "[许可证管理器] 云端验证失败:" << cloudResult;
            return verificationMode == CloudOnly ? Invalid : CloudCheckRequired;
        }
    }

    return Valid;
}

bool LicenseManager::verifyLicenseLocal(const QJsonObject &licenseData)
{
    // 检查许可证完整性
    if (!verifyLicenseIntegrity(licenseData, true)) {
        qDebug() << "[许可证管理器] 许可证完整性验证失败";
        return false;
    }

    // 检查过期时间
    QString expiryStr = licenseData["expiry"].toString();
    if (!expiryStr.isEmpty()) {
        QDateTime expiry = QDateTime::fromString(expiryStr, Qt::ISODate);
        if (expiry.isValid() && expiry < QDateTime::currentDateTime()) {
            qDebug() << "[许可证管理器] 许可证已过期:" << expiryStr;
            return false;
        }
    }

    // 检查硬件绑定（增强版，支持容错）
    QString storedDeviceId = licenseData["device_id"].toString();
    QString currentDeviceId = getCurrentDeviceId();

    // 使用增强的指纹验证，允许5%的容错
    if (!HardwareFingerprint::verifyFingerprint(storedDeviceId, currentDeviceId, 5)) {
        qDebug() << "[许可证管理器] 硬件指纹不匹配";
        return false;
    }

    // 验证设备绑定签名
    QString storedSignature = licenseData["binding_signature"].toString();
    QString currentSignature = generateDeviceBindingSignature(currentDeviceId,
                                                             licenseData["activation_code"].toString());
    if (!storedSignature.isEmpty() && storedSignature != currentSignature) {
        qDebug() << "[许可证管理器] 设备绑定签名不匹配";
        return false;
    }

    return true;
}

bool LicenseManager::shouldPerformCloudCheck()
{
    // 自适应验证逻辑：根据上次云端检查时间决定
    QDateTime lastCloudCheck = QDateTime::fromString(
        settings->value("license/last_cloud_check").toString(), Qt::ISODate);

    if (!lastCloudCheck.isValid()) {
        return true; // 从未进行过云端检查
    }

    // 如果超过24小时未进行云端检查，则需要检查
    return lastCloudCheck.addDays(1) < QDateTime::currentDateTime();
}

void LicenseManager::activateLicense(const QString &activationCode, const QString &deviceId)
{
    currentActivationCode = activationCode.trimmed().toUpper();
    currentDeviceId = deviceId.isEmpty() ? getCurrentDeviceId() : deviceId;
    
    qDebug() << "[许可证管理器] 开始激活:";
    qDebug() << "  激活码:" << currentActivationCode;
    qDebug() << "  设备ID:" << currentDeviceId;
    qDebug() << "  调试模式:" << debugMode;
    
    // 验证激活码格式
    if (!isValidActivationCodeFormat(currentActivationCode)) {
        emit activationFinished(InvalidCode, "激活码格式无效");
        return;
    }
    
    if (debugMode) {
        activateDebug(currentActivationCode);
    } else {
        activateProduction(currentActivationCode, currentDeviceId);
    }
}

QString LicenseManager::getCurrentDeviceId()
{
    if (debugMode) {
        // 调试模式使用固定的设备ID
        return DEBUG_DEVICE_PREFIX + "50bf70582c5ea2ac7502656f8cfb522e";
    }

    // 使用增强的硬件指纹（加盐处理）
    return HardwareFingerprint::getDeviceFingerprint(true, false);
}

QString LicenseManager::getDeviceLicense(const QString &key)
{
    // 仿YXPermission::getDeviceLicense
    return HardwareFingerprint::getDeviceLicense(key);
}

bool LicenseManager::deactivateLicense()
{
    // 仿YXPermission::deactive
    if (debugMode) {
        qDebug() << "[许可证管理器] 调试模式：模拟停用成功";
        clearLicense();
        return true;
    }

    QJsonObject license = loadLicense();
    if (license.isEmpty()) {
        qDebug() << "[许可证管理器] 没有可停用的许可证";
        return false;
    }

    // 构建停用请求
    QNetworkRequest request;
    request.setUrl(QUrl(SERVER_URL + DEACTIVATION_ENDPOINT));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QJsonObject requestData;
    requestData["activation_code"] = license["activation_code"].toString();
    requestData["device_id"] = getCurrentDeviceId();
    requestData["product_id"] = currentProductId;

    QJsonDocument doc(requestData);
    QByteArray data = doc.toJson();

    qDebug() << "[许可证管理器] 发送停用请求:" << data;

    // 发送请求（这里简化为同步处理）
    QNetworkReply *reply = networkManager->post(request, data);

    // 等待响应（实际应用中应该使用异步处理）
    QEventLoop loop;
    connect(reply, &QNetworkReply::finished, &loop, &QEventLoop::quit);
    loop.exec();

    bool success = (reply->error() == QNetworkReply::NoError);
    if (success) {
        clearLicense();
        qDebug() << "[许可证管理器] 许可证停用成功";
    } else {
        qDebug() << "[许可证管理器] 许可证停用失败:" << reply->errorString();
    }

    reply->deleteLater();
    return success;
}

bool LicenseManager::getLicenseDetails(QString &storeNo, QString &license)
{
    // 仿YXPermission::licenseInfo
    QJsonObject licenseData = loadLicense();
    if (licenseData.isEmpty()) {
        return false;
    }

    storeNo = licenseData["store_no"].toString();
    license = licenseData["activation_code"].toString();

    qDebug() << "[许可证管理器] 获取许可证详情 - 商店号:" << storeNo << "许可证:" << license;

    return !storeNo.isEmpty() && !license.isEmpty();
}

QJsonObject LicenseManager::getLicenseInfo()
{
    return loadLicense();
}

void LicenseManager::clearLicense()
{
    settings->clear();
    settings->sync();
    qDebug() << "[许可证管理器] 已清除本地许可证";
    emit licenseStatusChanged(false);
}

bool LicenseManager::isValidActivationCodeFormat(const QString &code)
{
    // 激活码格式：XXXX-XXXX-XXXX-XXXX（4组4位字符，用连字符分隔）
    QRegularExpression regex("^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$");
    return regex.match(code).hasMatch();
}

void LicenseManager::activateProduction(const QString &activationCode, const QString &deviceId)
{
    qDebug() << "[许可证管理器] 生产模式激活";

    // 构建请求
    QNetworkRequest request;
    request.setUrl(QUrl(SERVER_URL + ACTIVATION_ENDPOINT));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    // 构建请求数据
    QJsonObject requestData;
    requestData["activation_code"] = activationCode;
    requestData["device_id"] = deviceId;
    requestData["product_id"] = "40201";
    requestData["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    QJsonDocument doc(requestData);
    QByteArray data = doc.toJson();

    qDebug() << "[许可证管理器] 发送激活请求:" << data;

    // 发送请求
    QNetworkReply *reply = networkManager->post(request, data);
    connect(reply, &QNetworkReply::finished, this, &LicenseManager::onActivationReplyFinished);
    connect(reply, QOverload<QNetworkReply::NetworkError>::of(&QNetworkReply::error),
            this, &LicenseManager::onNetworkError);
}

void LicenseManager::activateDebug(const QString &activationCode)
{
    qDebug() << "[许可证管理器] 调试模式激活";

    // 检查是否是有效的调试激活码
    if (!DEBUG_VALID_CODES.contains(activationCode)) {
        emit activationFinished(InvalidCode, "调试模式：激活码无效");
        return;
    }

    // 创建调试许可证
    QJsonObject debugLicense;
    debugLicense["activation_code"] = activationCode;
    debugLicense["device_id"] = currentDeviceId;
    debugLicense["product_id"] = "40201";
    debugLicense["activated_at"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    debugLicense["expiry"] = QDateTime::currentDateTime().addYears(1).toString(Qt::ISODate);
    debugLicense["debug_mode"] = true;
    debugLicense["status"] = "active";

    // 保存调试许可证
    saveLicense(debugLicense);

    emit activationFinished(DebugModeSuccess, "调试模式激活成功");
    emit licenseStatusChanged(true);
}

void LicenseManager::onActivationReplyFinished()
{
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) return;

    reply->deleteLater();

    if (reply->error() != QNetworkReply::NoError) {
        qDebug() << "[许可证管理器] 网络错误:" << reply->errorString();
        emit activationFinished(NetworkError, "网络连接失败: " + reply->errorString());
        return;
    }

    // 解析响应
    QByteArray responseData = reply->readAll();
    qDebug() << "[许可证管理器] 服务器响应:" << responseData;

    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(responseData, &parseError);

    if (parseError.error != QJsonParseError::NoError) {
        qDebug() << "[许可证管理器] JSON解析错误:" << parseError.errorString();
        emit activationFinished(ServerError, "服务器响应格式错误");
        return;
    }

    QJsonObject response = doc.object();
    QString status = response["status"].toString();
    QString message = response["message"].toString();

    if (status == "success") {
        // 激活成功，保存许可证
        QJsonObject licenseData = response["license"].toObject();
        licenseData["device_id"] = currentDeviceId;
        licenseData["activated_at"] = QDateTime::currentDateTime().toString(Qt::ISODate);

        saveLicense(licenseData);

        emit activationFinished(Success, "激活成功");
        emit licenseStatusChanged(true);
    } else {
        // 激活失败
        ActivationResult result = InvalidCode;
        if (message.contains("hardware", Qt::CaseInsensitive)) {
            result = HardwareMismatch;
        } else if (message.contains("expired", Qt::CaseInsensitive)) {
            result = ExpiredLicense;
        } else if (message.contains("activated", Qt::CaseInsensitive)) {
            result = AlreadyActivated;
        }

        emit activationFinished(result, message);
    }
}

void LicenseManager::onNetworkError(QNetworkReply::NetworkError error)
{
    Q_UNUSED(error)
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (reply) {
        qDebug() << "[许可证管理器] 网络错误:" << reply->errorString();
        emit activationFinished(NetworkError, "网络连接失败: " + reply->errorString());
    }
}

void LicenseManager::saveLicense(const QJsonObject &licenseData)
{
    QString deviceId = licenseData["device_id"].toString();
    QString encryptionKey = generateEncryptionKey(deviceId);

    // 序列化许可证数据
    QJsonDocument doc(licenseData);
    QString jsonString = doc.toJson(QJsonDocument::Compact);

    // 加密许可证数据
    QString encryptedData = encryptData(jsonString, encryptionKey);

    // 保存到本地
    settings->setValue("license/data", encryptedData);
    settings->setValue("license/checksum", generateMD5(jsonString + deviceId));
    settings->sync();

    qDebug() << "[许可证管理器] 许可证已保存到本地";
}

QJsonObject LicenseManager::loadLicense()
{
    QString encryptedData = settings->value("license/data").toString();
    if (encryptedData.isEmpty()) {
        qDebug() << "[许可证管理器] 没有找到许可证数据";
        return QJsonObject();
    }

    QString deviceId = getCurrentDeviceId();
    QString encryptionKey = generateEncryptionKey(deviceId);

    // 解密许可证数据
    QString jsonString = decryptData(encryptedData, encryptionKey);
    if (jsonString.isEmpty()) {
        qDebug() << "[许可证管理器] 许可证解密失败，可能是设备指纹变化";
        // 清除无效的许可证数据
        settings->remove("license/data");
        settings->remove("license/checksum");
        settings->sync();
        return QJsonObject();
    }

    // 验证JSON格式
    if (jsonString.trimmed().isEmpty() || jsonString == "null") {
        qDebug() << "[许可证管理器] 许可证数据为空";
        return QJsonObject();
    }

    // 解析JSON
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8(), &parseError);
    if (parseError.error != QJsonParseError::NoError) {
        qDebug() << "[许可证管理器] 许可证JSON解析失败:" << parseError.errorString();
        qDebug() << "[许可证管理器] 原始数据:" << jsonString;
        // 清除损坏的许可证数据
        settings->remove("license/data");
        settings->remove("license/checksum");
        settings->sync();
        return QJsonObject();
    }

    return doc.object();
}

QString LicenseManager::encryptData(const QString &data, const QString &key)
{
    // 简单的XOR加密（生产环境应使用更强的加密算法）
    QByteArray dataBytes = data.toUtf8();
    QByteArray keyBytes = key.toUtf8();
    QByteArray encrypted;

    for (int i = 0; i < dataBytes.size(); ++i) {
        encrypted.append(dataBytes[i] ^ keyBytes[i % keyBytes.size()]);
    }

    return encrypted.toBase64();
}

QString LicenseManager::decryptData(const QString &encryptedData, const QString &key)
{
    QByteArray encrypted = QByteArray::fromBase64(encryptedData.toUtf8());
    QByteArray keyBytes = key.toUtf8();
    QByteArray decrypted;

    for (int i = 0; i < encrypted.size(); ++i) {
        decrypted.append(encrypted[i] ^ keyBytes[i % keyBytes.size()]);
    }

    return QString::fromUtf8(decrypted);
}

QString LicenseManager::generateEncryptionKey(const QString &deviceId)
{
    // 基于设备ID生成加密密钥
    QString salt = "AutobitModelConverter2024";
    return generateMD5(deviceId + salt);
}

QString LicenseManager::generateMD5(const QString &input)
{
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(input.toUtf8());
    return hash.result().toHex();
}

QString LicenseManager::generateSHA256(const QString &input)
{
    QCryptographicHash hash(QCryptographicHash::Sha256);
    hash.addData(input.toUtf8());
    return hash.result().toHex();
}

QString LicenseManager::generateDeviceBindingSignature(const QString &deviceId, const QString &licenseCode)
{
    // 生成设备绑定签名（仿YXPermission的安全策略）
    QString combined = deviceId + licenseCode + BINDING_SALT;
    return generateSHA256(combined);
}

LicenseManager::ActivationResult LicenseManager::verifyLicenseCloud(const QString &productId)
{
    // 仿YXPermission::checkLicense的云端验证逻辑
    QJsonObject license = loadLicense();
    if (license.isEmpty()) {
        return InvalidCode;
    }

    // 构建验证请求
    QNetworkRequest request;
    request.setUrl(QUrl(SERVER_URL + VERIFICATION_ENDPOINT));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QJsonObject requestData;
    requestData["activation_code"] = license["activation_code"].toString();
    requestData["device_id"] = getCurrentDeviceId();
    requestData["product_id"] = productId;
    requestData["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    QJsonDocument doc(requestData);
    QByteArray data = doc.toJson();

    qDebug() << "[许可证管理器] 发送云端验证请求:" << data;

    // 发送请求（简化为同步处理）
    QNetworkReply *reply = networkManager->post(request, data);

    QEventLoop loop;
    connect(reply, &QNetworkReply::finished, &loop, &QEventLoop::quit);
    loop.exec();

    ActivationResult result = NetworkError;

    if (reply->error() == QNetworkReply::NoError) {
        QJsonParseError parseError;
        QJsonDocument responseDoc = QJsonDocument::fromJson(reply->readAll(), &parseError);

        if (parseError.error == QJsonParseError::NoError) {
            QJsonObject response = responseDoc.object();
            bool isValid = response["valid"].toBool();

            if (isValid) {
                result = Success;
                // 更新最后云端检查时间
                settings->setValue("license/last_cloud_check",
                                 QDateTime::currentDateTime().toString(Qt::ISODate));
                settings->sync();
                qDebug() << "[许可证管理器] 云端验证成功";
            } else {
                QString reason = response["reason"].toString();
                qDebug() << "[许可证管理器] 云端验证失败:" << reason;

                if (reason.contains("expired")) {
                    result = ExpiredLicense;
                } else if (reason.contains("hardware")) {
                    result = HardwareMismatch;
                } else {
                    result = InvalidCode;
                }
            }
        } else {
            qDebug() << "[许可证管理器] 云端响应解析失败:" << parseError.errorString();
            result = ServerError;
        }
    } else {
        qDebug() << "[许可证管理器] 云端验证网络错误:" << reply->errorString();
        result = NetworkError;
    }

    reply->deleteLater();
    return result;
}

bool LicenseManager::verifyLicenseIntegrity(const QJsonObject &licenseData)
{
    QString storedChecksum = settings->value("license/checksum").toString();
    if (storedChecksum.isEmpty()) {
        return false;
    }

    QString deviceId = licenseData["device_id"].toString();
    QJsonDocument doc(licenseData);
    QString jsonString = doc.toJson(QJsonDocument::Compact);
    QString calculatedChecksum = generateMD5(jsonString + deviceId);

    return storedChecksum == calculatedChecksum;
}
