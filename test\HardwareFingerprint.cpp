#include "HardwareFingerprint.h"
#include <QProcess>
#include <QRegularExpression>
#include <QStandardPaths>

// 常量定义（RK3588专用，仿YXPermission）
const QString HardwareFingerprint::DEFAULT_SALT = "RK3588_AutobitTest2024_Enhanced";
const QString HardwareFingerprint::LICENSE_SALT = "245ba9b4-a074-499c-974c-5377b15c699f";
const QString HardwareFingerprint::RK3588_DEVICE_PREFIX = "RK3588_";

QString HardwareFingerprint::getDeviceFingerprint(bool useSalt, bool useMultiHash)
{
    QStringList components;

    // 收集各种硬件信息
    components << getCpuInfo();
    components << getMotherboardInfo();
    components << getMacAddress();
    components << getDiskSerial();
    components << getSystemInfo();

    // 添加RK3588特定信息
    components << getRK3588SpecificInfo();

    // 移除空值
    components.removeAll("");

    // 组合所有信息
    QString combined = components.join("|");

    qDebug() << "[硬件指纹] RK3588组合信息:" << combined;

    QString fingerprint;

    if (useMultiHash) {
        // 多重哈希：先MD5再SHA256
        QString md5Hash = generateMD5(combined);
        fingerprint = useSalt ? generateSaltedHash(md5Hash, DEFAULT_SALT, QCryptographicHash::Sha256)
                              : generateSHA256(md5Hash);
        qDebug() << "[硬件指纹] RK3588使用多重哈希 (MD5->SHA256)";
    } else {
        // 单一哈希
        fingerprint = useSalt ? generateSaltedHash(combined, DEFAULT_SALT)
                              : generateMD5(combined);
        qDebug() << "[硬件指纹] RK3588使用" << (useSalt ? "加盐MD5" : "标准MD5");
    }

    qDebug() << "[硬件指纹] RK3588生成指纹:" << fingerprint;

    return fingerprint;
}

QString HardwareFingerprint::getDeviceLicense(const QString &key)
{
    // 仿YXPermission::getDeviceLicense逻辑
    QString combined = key + LICENSE_SALT;
    QString license = generateSHA256(combined);

    qDebug() << "[设备许可证] RK3588输入密钥:" << key;
    qDebug() << "[设备许可证] RK3588生成许可证:" << license;

    return license;
}

QString HardwareFingerprint::getPrimaryDeviceId()
{
    // 仿YXPermission::getDeviceNo逻辑，针对RK3588优化
    QString primaryId;

    // RK3588平台：优先使用设备树信息
    primaryId = getRK3588SpecificInfo();
    if (primaryId.isEmpty()) {
        primaryId = getMotherboardInfo();
    }
    if (primaryId.isEmpty()) {
        primaryId = getMacAddress();
    }

    if (primaryId.isEmpty()) {
        // 备用方案：使用完整指纹
        primaryId = getDeviceFingerprint(false, false);
    }

    qDebug() << "[主设备标识] RK3588:" << primaryId;

    return RK3588_DEVICE_PREFIX + primaryId;
}

QString HardwareFingerprint::getCpuInfo()
{
    QString cpuInfo;
    
    // Linux ARM64: 从/proc/cpuinfo获取CPU信息
    cpuInfo = executeCommand("cat /proc/cpuinfo | grep 'model name' | head -1 | cut -d':' -f2");
    if (cpuInfo.isEmpty()) {
        cpuInfo = executeCommand("cat /proc/cpuinfo | grep 'Hardware' | head -1 | cut -d':' -f2");
    }
    if (cpuInfo.isEmpty()) {
        cpuInfo = executeCommand("cat /proc/cpuinfo | grep 'processor' | wc -l");
    }

    return cleanString(cpuInfo);
}

QString HardwareFingerprint::getMotherboardInfo()
{
    QString mbInfo;
    
    // Linux: 使用dmidecode获取主板信息
    mbInfo = executeCommand("sudo dmidecode -s baseboard-serial-number 2>/dev/null");
    if (mbInfo.isEmpty() || mbInfo.contains("Not Specified")) {
        mbInfo = executeCommand("sudo dmidecode -s baseboard-product-name 2>/dev/null");
    }
    if (mbInfo.isEmpty()) {
        // 备用方案：使用/sys/class/dmi/id/
        mbInfo = executeCommand("cat /sys/class/dmi/id/board_serial 2>/dev/null");
    }
    if (mbInfo.isEmpty()) {
        // ARM64设备特殊处理：使用设备树信息
        mbInfo = executeCommand("cat /proc/device-tree/model 2>/dev/null");
    }
    
    return cleanString(mbInfo);
}

QString HardwareFingerprint::getMacAddress()
{
    // 获取第一个有效的网卡MAC地址
    QList<QNetworkInterface> interfaces = QNetworkInterface::allInterfaces();
    
    for (const QNetworkInterface &interface : interfaces) {
        // 跳过回环接口和虚拟接口
        if (interface.flags() & QNetworkInterface::IsLoopBack ||
            interface.name().contains("docker") ||
            interface.name().contains("veth") ||
            interface.name().contains("br-") ||
            interface.name().contains("lo")) {
            continue;
        }
        
        QString mac = interface.hardwareAddress();
        if (!mac.isEmpty() && mac != "00:00:00:00:00:00") {
            return cleanString(mac);
        }
    }
    
    return QString();
}

QString HardwareFingerprint::getDiskSerial()
{
    QString diskSerial;
    
    // Linux: 获取根分区所在磁盘的序列号
    diskSerial = executeCommand("lsblk -no SERIAL $(df / | tail -1 | awk '{print $1}' | sed 's/[0-9]*$//')");
    if (diskSerial.isEmpty()) {
        // 备用方案：使用udevadm
        diskSerial = executeCommand("udevadm info --query=property --name=$(df / | tail -1 | awk '{print $1}' | sed 's/[0-9]*$//') | grep ID_SERIAL_SHORT | cut -d'=' -f2");
    }
    if (diskSerial.isEmpty()) {
        // 再备用方案：使用blkid
        diskSerial = executeCommand("blkid $(df / | tail -1 | awk '{print $1}' | sed 's/[0-9]*$//') | grep -o 'UUID=\"[^\"]*\"' | cut -d'\"' -f2");
    }
    
    return cleanString(diskSerial);
}

QString HardwareFingerprint::getSystemInfo()
{
    QString sysInfo;
    
    // 获取系统基本信息
    sysInfo += QSysInfo::kernelType();
    sysInfo += QSysInfo::kernelVersion();
    sysInfo += QSysInfo::currentCpuArchitecture();
    
    // 添加RK3588特定信息
    QString rk3588Info = executeCommand("cat /proc/device-tree/compatible 2>/dev/null");
    if (!rk3588Info.isEmpty()) {
        sysInfo += rk3588Info;
    }
    
    return cleanString(sysInfo);
}

bool HardwareFingerprint::verifyFingerprint(const QString &storedFingerprint, 
                                           const QString &currentFingerprint)
{
    QString current = currentFingerprint.isEmpty() ? getDeviceFingerprint() : currentFingerprint;
    
    qDebug() << "[硬件指纹] 验证指纹:";
    qDebug() << "  存储的:" << storedFingerprint;
    qDebug() << "  当前的:" << current;
    
    return storedFingerprint == current;
}

QString HardwareFingerprint::executeCommand(const QString &command)
{
    QProcess process;
    process.start("bash", QStringList() << "-c" << command);
    process.waitForFinished(5000); // 5秒超时
    
    if (process.exitCode() == 0) {
        return QString::fromLocal8Bit(process.readAllStandardOutput()).trimmed();
    }
    
    return QString();
}

QString HardwareFingerprint::cleanString(const QString &input)
{
    QString cleaned = input;
    
    // 移除空白字符
    cleaned = cleaned.trimmed();
    
    // 移除常见的无效值
    QStringList invalidValues = {
        "Not Specified", "Not Available", "To Be Filled By O.E.M.",
        "Default string", "System Serial Number", "System Product Name",
        "N/A", "Unknown", "None"
    };
    
    for (const QString &invalid : invalidValues) {
        if (cleaned.contains(invalid, Qt::CaseInsensitive)) {
            return QString();
        }
    }
    
    // 移除特殊字符，只保留字母数字和连字符
    cleaned = cleaned.replace(QRegularExpression("[^a-zA-Z0-9\\-]"), "");
    
    return cleaned;
}

QString HardwareFingerprint::generateMD5(const QString &input)
{
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(input.toUtf8());
    return hash.result().toHex();
}

QString HardwareFingerprint::generateSHA256(const QString &input)
{
    QCryptographicHash hash(QCryptographicHash::Sha256);
    hash.addData(input.toUtf8());
    return hash.result().toHex();
}

QString HardwareFingerprint::generateSaltedHash(const QString &input, const QString &salt,
                                               QCryptographicHash::Algorithm algorithm)
{
    // 仿YXPermission的加盐逻辑
    QString saltedInput = input + salt;

    QCryptographicHash hash(algorithm);
    hash.addData(saltedInput.toUtf8());
    QString result = hash.result().toHex();

    qDebug() << "[加盐哈希] RK3588算法:" << (algorithm == QCryptographicHash::Md5 ? "MD5" : "SHA256");
    qDebug() << "[加盐哈希] RK3588输入长度:" << input.length() << "盐值长度:" << salt.length();

    return result;
}

QString HardwareFingerprint::getRK3588SpecificInfo()
{
    QString rk3588Info;

    // RK3588特定的设备树信息
    QString deviceTree = executeCommand("cat /proc/device-tree/compatible 2>/dev/null");
    if (!deviceTree.isEmpty() && deviceTree.contains("rk3588")) {
        rk3588Info += deviceTree;
    }

    // RK3588 CPU序列号
    QString cpuSerial = executeCommand("cat /proc/cpuinfo | grep Serial | head -1 | cut -d':' -f2");
    if (!cpuSerial.isEmpty()) {
        rk3588Info += cpuSerial;
    }

    // RK3588 SoC信息
    QString socInfo = executeCommand("cat /sys/devices/platform/soc/soc:internal-regs/ff770000.syscon/of_node/compatible 2>/dev/null");
    if (!socInfo.isEmpty()) {
        rk3588Info += socInfo;
    }

    // RK3588 板级信息
    QString boardInfo = executeCommand("cat /proc/device-tree/model 2>/dev/null");
    if (!boardInfo.isEmpty()) {
        rk3588Info += boardInfo;
    }

    qDebug() << "[RK3588特定信息]:" << rk3588Info;

    return cleanString(rk3588Info);
}

QString HardwareFingerprint::getRK3588PlatformInfo()
{
    QString platformInfo;

    // RK3588平台特定的硬件信息收集
    QString rockchipInfo = executeCommand("cat /proc/version | grep -o 'rockchip[^\\s]*'");
    if (!rockchipInfo.isEmpty()) {
        platformInfo += rockchipInfo;
    }

    QString armInfo = executeCommand("cat /proc/cpuinfo | grep 'CPU architecture' | head -1 | cut -d':' -f2");
    if (!armInfo.isEmpty()) {
        platformInfo += armInfo;
    }

    return cleanString(platformInfo);
}
