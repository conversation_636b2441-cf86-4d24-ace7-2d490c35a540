#ifndef LICENSEMANAGER_H
#define LICENSEMANAGER_H

#include <QObject>
#include <QString>
#include <QDateTime>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonObject>
#include <QJsonDocument>
#include <QSettings>
#include <QCryptographicHash>
#include <QTimer>

/**
 * @brief 许可证管理器（增强版 - RK3588专用）
 *
 * 负责激活码验证、许可证管理、硬件绑定等功能
 * 支持生产模式（在线验证+硬件绑定）和调试模式（离线验证）
 * 适用于RK3588边缘设备，采用YXPermission的安全增强策略
 *
 * 主要改进（仿YXPermission）：
 * - 增强加密算法（SHA256）
 * - 改进设备绑定策略
 * - 支持多种验证模式
 * - 增强许可证完整性验证
 * - 支持云端+本地双重验证
 * - 针对RK3588边缘设备优化
 */
class LicenseManager : public QObject
{
    Q_OBJECT

public:
    enum ActivationResult {
        Success,                // 激活成功
        InvalidCode,           // 激活码无效
        NetworkError,          // 网络错误
        HardwareMismatch,      // 硬件不匹配
        ServerError,           // 服务器错误
        AlreadyActivated,      // 已经激活
        ExpiredLicense,        // 许可证过期
        DebugModeSuccess,      // 调试模式激活成功
        CloudVerifyFailed,     // 云端验证失败
        LocalVerifyFailed,     // 本地验证失败
        DeviceBindingFailed,   // 设备绑定失败
        RK3588SpecificError    // RK3588特定错误
    };

    enum LicenseStatus {
        NotActivated,          // 未激活
        Valid,                 // 有效
        Expired,               // 已过期
        Invalid,               // 无效
        CloudCheckRequired     // 需要云端检查
    };

    enum VerificationMode {
        LocalOnly,             // 仅本地验证
        CloudOnly,             // 仅云端验证
        LocalAndCloud,         // 本地+云端验证
        Adaptive,              // 自适应验证
        EdgeOptimized          // 边缘设备优化模式（RK3588专用）
    };

    explicit LicenseManager(QObject *parent = nullptr);
    
    /**
     * @brief 设置调试模式
     * @param enabled 是否启用调试模式
     */
    void setDebugMode(bool enabled);

    /**
     * @brief 设置验证模式（新增）
     * @param mode 验证模式
     */
    void setVerificationMode(VerificationMode mode);

    /**
     * @brief 检查当前许可证状态（增强版）
     * @param forceCloudCheck 是否强制云端检查
     * @return 许可证状态
     */
    LicenseStatus checkLicenseStatus(bool forceCloudCheck = false);

    /**
     * @brief 激活许可证
     * @param activationCode 激活码
     * @param deviceId 设备ID（可选，为空时自动获取）
     */
    void activateLicense(const QString &activationCode, const QString &deviceId = QString());

    /**
     * @brief 停用许可证（仿YXPermission::deactive）
     * @return 是否成功
     */
    bool deactivateLicense();

    /**
     * @brief 获取当前设备ID
     * @return 设备ID
     */
    QString getCurrentDeviceId();

    /**
     * @brief 获取设备许可证标识（仿YXPermission::getDeviceLicense）
     * @param key 自定义密钥
     * @return 设备许可证标识
     */
    QString getDeviceLicense(const QString &key);

    /**
     * @brief 获取许可证信息
     * @return 许可证信息JSON对象
     */
    QJsonObject getLicenseInfo();

    /**
     * @brief 获取许可证详细信息（仿YXPermission::licenseInfo）
     * @param storeNo 输出：商店编号
     * @param license 输出：许可证号
     * @return 是否成功
     */
    bool getLicenseDetails(QString &storeNo, QString &license);

    /**
     * @brief 清除本地许可证
     */
    void clearLicense();

    /**
     * @brief 验证激活码格式
     * @param code 激活码
     * @return 是否有效
     */
    static bool isValidActivationCodeFormat(const QString &code);

    /**
     * @brief 获取RK3588设备状态
     * @return RK3588特定状态信息
     */
    QString getRK3588DeviceStatus();

signals:
    /**
     * @brief 激活完成信号
     * @param result 激活结果
     * @param message 结果消息
     */
    void activationFinished(ActivationResult result, const QString &message);
    
    /**
     * @brief 许可证状态变化信号
     * @param isValid 是否有效
     */
    void licenseStatusChanged(bool isValid);

private slots:
    void onActivationReplyFinished();
    void onNetworkError(QNetworkReply::NetworkError error);

private:
    /**
     * @brief 生产模式激活
     * @param activationCode 激活码
     * @param deviceId 设备ID
     */
    void activateProduction(const QString &activationCode, const QString &deviceId);
    
    /**
     * @brief 调试模式激活
     * @param activationCode 激活码
     */
    void activateDebug(const QString &activationCode);
    
    /**
     * @brief 保存许可证到本地
     * @param licenseData 许可证数据
     */
    void saveLicense(const QJsonObject &licenseData);
    
    /**
     * @brief 从本地加载许可证
     * @return 许可证数据
     */
    QJsonObject loadLicense();
    
    /**
     * @brief 加密数据
     * @param data 原始数据
     * @param key 加密密钥
     * @return 加密后的数据
     */
    QString encryptData(const QString &data, const QString &key);
    
    /**
     * @brief 解密数据
     * @param encryptedData 加密的数据
     * @param key 解密密钥
     * @return 解密后的数据
     */
    QString decryptData(const QString &encryptedData, const QString &key);
    
    /**
     * @brief 生成加密密钥
     * @param deviceId 设备ID
     * @return 加密密钥
     */
    QString generateEncryptionKey(const QString &deviceId);
    
    /**
     * @brief 验证许可证完整性
     * @param licenseData 许可证数据
     * @return 是否有效
     */
    bool verifyLicenseIntegrity(const QJsonObject &licenseData);
    
    /**
     * @brief 生成MD5哈希
     * @param input 输入字符串
     * @return MD5哈希值
     */
    QString generateMD5(const QString &input);

private:
    QNetworkAccessManager *networkManager;
    QSettings *settings;
    bool debugMode;
    QString currentActivationCode;
    QString currentDeviceId;
    
    // 服务器配置
    static const QString SERVER_URL;
    static const QString ACTIVATION_ENDPOINT;
    static const QString VERIFICATION_ENDPOINT;
    
    // 调试模式配置
    static const QStringList DEBUG_VALID_CODES;
    static const QString DEBUG_DEVICE_PREFIX;
};

#endif // LICENSEMANAGER_H
