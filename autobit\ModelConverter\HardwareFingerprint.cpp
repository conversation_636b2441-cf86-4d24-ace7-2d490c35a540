#include "HardwareFingerprint.h"
#include <QProcess>
#include <QRegularExpression>
#include <QStandardPaths>

// 常量定义（仿YXPermission）
const QString HardwareFingerprint::DEFAULT_SALT = "AutobitModelConverter2024_Enhanced";
const QString HardwareFingerprint::LICENSE_SALT = "245ba9b4-a074-499c-974c-5377b15c699f";
const QString HardwareFingerprint::DEVICE_PREFIX = "AUTOBIT_";

QString HardwareFingerprint::getDeviceFingerprint(bool useSalt, bool useMultiHash)
{
    QStringList components;

    // 收集各种硬件信息
    components << getCpuInfo();
    components << getMotherboardInfo();
    components << getMacAddress();
    components << getDiskSerial();
    components << getSystemInfo();

    // 添加平台特定信息
    components << getPlatformSpecificInfo();

    // 移除空值
    components.removeAll("");

    // 组合所有信息
    QString combined = components.join("|");

    qDebug() << "[硬件指纹] 组合信息:" << combined;

    QString fingerprint;

    if (useMultiHash) {
        // 多重哈希：先MD5再SHA256
        QString md5Hash = generateMD5(combined);
        fingerprint = useSalt ? generateSaltedHash(md5Hash, DEFAULT_SALT, QCryptographicHash::Sha256)
                              : generateSHA256(md5Hash);
        qDebug() << "[硬件指纹] 使用多重哈希 (MD5->SHA256)";
    } else {
        // 单一哈希
        fingerprint = useSalt ? generateSaltedHash(combined, DEFAULT_SALT)
                              : generateMD5(combined);
        qDebug() << "[硬件指纹] 使用" << (useSalt ? "加盐MD5" : "标准MD5");
    }

    qDebug() << "[硬件指纹] 生成指纹:" << fingerprint;

    return fingerprint;
}

QString HardwareFingerprint::getDeviceLicense(const QString &key)
{
    // 仿YXPermission::getDeviceLicense逻辑
    QString combined = key + LICENSE_SALT;
    QString license = generateSHA256(combined);

    qDebug() << "[设备许可证] 输入密钥:" << key;
    qDebug() << "[设备许可证] 生成许可证:" << license;

    return license;
}

QString HardwareFingerprint::getPrimaryDeviceId()
{
    // 仿YXPermission::getDeviceNo逻辑，优先使用最稳定的硬件标识
    QString primaryId;

#ifdef Q_OS_WIN
    // Windows平台：优先使用机器GUID
    primaryId = getWindowsMachineGUID();
    if (primaryId.isEmpty()) {
        primaryId = getWindowsBaseBoardUUID();
    }
#else
    // Linux平台：优先使用主板信息
    primaryId = getMotherboardInfo();
    if (primaryId.isEmpty()) {
        primaryId = getMacAddress();
    }
#endif

    if (primaryId.isEmpty()) {
        // 备用方案：使用完整指纹
        primaryId = getDeviceFingerprint(false, false);
    }

    qDebug() << "[主设备标识]:" << primaryId;

    return DEVICE_PREFIX + primaryId;
}

QString HardwareFingerprint::getCpuInfo()
{
    QString cpuInfo;

#ifdef Q_OS_LINUX
    // Linux: 从/proc/cpuinfo获取CPU信息
    cpuInfo = executeCommand("cat /proc/cpuinfo | grep 'model name' | head -1 | cut -d':' -f2");
    if (cpuInfo.isEmpty()) {
        cpuInfo = executeCommand("cat /proc/cpuinfo | grep 'processor' | wc -l");
    }
    // WSL2特殊处理：如果还是空的，使用备用方案
    if (cpuInfo.isEmpty()) {
        cpuInfo = executeCommand("uname -m");
        if (cpuInfo.isEmpty()) {
            cpuInfo = "WSL2-CPU";  // WSL2环境下的默认标识
        }
    }
#elif defined(Q_OS_WIN)
    // Windows: 使用wmic获取CPU信息
    cpuInfo = executeCommand("wmic cpu get ProcessorId /value | findstr ProcessorId");
    if (cpuInfo.isEmpty()) {
        cpuInfo = executeCommand("wmic cpu get Name /value | findstr Name");
    }
#endif
    
    return cleanString(cpuInfo);
}

QString HardwareFingerprint::getMotherboardInfo()
{
    QString mbInfo;
    
#ifdef Q_OS_LINUX
    // Linux: 使用dmidecode获取主板信息
    mbInfo = executeCommand("sudo dmidecode -s baseboard-serial-number 2>/dev/null");
    if (mbInfo.isEmpty() || mbInfo.contains("Not Specified")) {
        mbInfo = executeCommand("sudo dmidecode -s baseboard-product-name 2>/dev/null");
    }
    if (mbInfo.isEmpty()) {
        // 备用方案：使用/sys/class/dmi/id/
        mbInfo = executeCommand("cat /sys/class/dmi/id/board_serial 2>/dev/null");
    }
#elif defined(Q_OS_WIN)
    // Windows: 使用wmic获取主板信息
    mbInfo = executeCommand("wmic baseboard get SerialNumber /value | findstr SerialNumber");
    if (mbInfo.isEmpty()) {
        mbInfo = executeCommand("wmic baseboard get Product /value | findstr Product");
    }
#endif
    
    return cleanString(mbInfo);
}

QString HardwareFingerprint::getMacAddress()
{
    // 获取第一个有效的网卡MAC地址
    QList<QNetworkInterface> interfaces = QNetworkInterface::allInterfaces();

    for (const QNetworkInterface &interface : interfaces) {
        // 跳过回环接口和虚拟接口
        if (interface.flags() & QNetworkInterface::IsLoopBack ||
            interface.name().contains("docker") ||
            interface.name().contains("veth") ||
            interface.name().contains("br-")) {
            continue;
        }

        QString mac = interface.hardwareAddress();
        if (!mac.isEmpty() && mac != "00:00:00:00:00:00") {
            return cleanString(mac);
        }
    }

    // WSL2环境下的备用方案
    QString wslMac = executeCommand("cat /sys/class/net/eth0/address 2>/dev/null");
    if (!wslMac.isEmpty()) {
        return cleanString(wslMac);
    }

    // 最后的备用方案：使用主机名
    QString hostname = executeCommand("hostname");
    if (!hostname.isEmpty()) {
        return cleanString(hostname);
    }

    return "WSL2-DEFAULT-MAC";
}

QString HardwareFingerprint::getDiskSerial()
{
    QString diskSerial;
    
#ifdef Q_OS_LINUX
    // Linux: 获取根分区所在磁盘的序列号
    diskSerial = executeCommand("lsblk -no SERIAL $(df / | tail -1 | awk '{print $1}' | sed 's/[0-9]*$//')");
    if (diskSerial.isEmpty()) {
        // 备用方案：使用udevadm
        diskSerial = executeCommand("udevadm info --query=property --name=$(df / | tail -1 | awk '{print $1}' | sed 's/[0-9]*$//') | grep ID_SERIAL_SHORT | cut -d'=' -f2");
    }
#elif defined(Q_OS_WIN)
    // Windows: 使用wmic获取磁盘序列号
    diskSerial = executeCommand("wmic diskdrive get SerialNumber /value | findstr SerialNumber");
#endif
    
    return cleanString(diskSerial);
}

QString HardwareFingerprint::getSystemInfo()
{
    QString sysInfo;

    // 获取系统基本信息
    sysInfo += QSysInfo::kernelType();
    sysInfo += QSysInfo::kernelVersion();
    sysInfo += QSysInfo::currentCpuArchitecture();

    // WSL2环境检测
    QString wslVersion = executeCommand("cat /proc/version 2>/dev/null | grep -i microsoft");
    if (!wslVersion.isEmpty()) {
        sysInfo += "WSL2";
    }

    // 添加用户名作为额外标识
    QString username = executeCommand("whoami");
    if (!username.isEmpty()) {
        sysInfo += username;
    }

    return cleanString(sysInfo);
}

bool HardwareFingerprint::verifyFingerprint(const QString &storedFingerprint,
                                           const QString &currentFingerprint,
                                           int tolerance)
{
    QString current = currentFingerprint.isEmpty() ? getDeviceFingerprint() : currentFingerprint;

    qDebug() << "[硬件指纹] 验证指纹:";
    qDebug() << "  存储的:" << storedFingerprint;
    qDebug() << "  当前的:" << current;
    qDebug() << "  容错级别:" << tolerance;

    if (tolerance == 0) {
        // 完全匹配
        return storedFingerprint == current;
    } else {
        // 容错匹配：比较部分硬件信息的一致性
        // 这里可以实现更复杂的容错逻辑
        // 例如：允许某些硬件信息变化但核心信息保持一致

        // 简单实现：检查前缀匹配度
        int minLength = qMin(storedFingerprint.length(), current.length());
        int matchCount = 0;

        for (int i = 0; i < minLength; ++i) {
            if (storedFingerprint[i] == current[i]) {
                matchCount++;
            }
        }

        int matchPercentage = (matchCount * 100) / minLength;
        bool isMatch = matchPercentage >= (100 - tolerance);

        qDebug() << "  匹配度:" << matchPercentage << "% (要求:" << (100 - tolerance) << "%)";

        return isMatch;
    }
}

QString HardwareFingerprint::executeCommand(const QString &command)
{
    QProcess process;
    process.start("bash", QStringList() << "-c" << command);
    process.waitForFinished(5000); // 5秒超时
    
    if (process.exitCode() == 0) {
        return QString::fromLocal8Bit(process.readAllStandardOutput()).trimmed();
    }
    
    return QString();
}

QString HardwareFingerprint::cleanString(const QString &input)
{
    QString cleaned = input;
    
    // 移除空白字符
    cleaned = cleaned.trimmed();
    
    // 移除常见的无效值
    QStringList invalidValues = {
        "Not Specified", "Not Available", "To Be Filled By O.E.M.",
        "Default string", "System Serial Number", "System Product Name",
        "N/A", "Unknown", "None"
    };
    
    for (const QString &invalid : invalidValues) {
        if (cleaned.contains(invalid, Qt::CaseInsensitive)) {
            return QString();
        }
    }
    
    // 移除特殊字符，只保留字母数字和连字符
    cleaned = cleaned.replace(QRegularExpression("[^a-zA-Z0-9\\-]"), "");
    
    return cleaned;
}

QString HardwareFingerprint::generateMD5(const QString &input)
{
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(input.toUtf8());
    return hash.result().toHex();
}

QString HardwareFingerprint::generateSHA256(const QString &input)
{
    QCryptographicHash hash(QCryptographicHash::Sha256);
    hash.addData(input.toUtf8());
    return hash.result().toHex();
}

QString HardwareFingerprint::generateSaltedHash(const QString &input, const QString &salt,
                                               QCryptographicHash::Algorithm algorithm)
{
    // 仿YXPermission的加盐逻辑
    QString saltedInput = input + salt;

    QCryptographicHash hash(algorithm);
    hash.addData(saltedInput.toUtf8());
    QString result = hash.result().toHex();

    qDebug() << "[加盐哈希] 算法:" << (algorithm == QCryptographicHash::Md5 ? "MD5" : "SHA256");
    qDebug() << "[加盐哈希] 输入长度:" << input.length() << "盐值长度:" << salt.length();

    return result;
}

QString HardwareFingerprint::getPlatformSpecificInfo()
{
    QString platformInfo;

#ifdef Q_OS_WIN
    // Windows平台特定信息
    platformInfo += getWindowsMachineGUID();
    platformInfo += getWindowsBaseBoardUUID();
#else
    // Linux平台特定信息
    QString deviceTree = executeCommand("cat /proc/device-tree/compatible 2>/dev/null");
    if (!deviceTree.isEmpty()) {
        platformInfo += deviceTree;
    }

    QString cpuSerial = executeCommand("cat /proc/cpuinfo | grep Serial | head -1 | cut -d':' -f2");
    if (!cpuSerial.isEmpty()) {
        platformInfo += cpuSerial;
    }
#endif

    return cleanString(platformInfo);
}

QString HardwareFingerprint::getWindowsMachineGUID()
{
#ifdef Q_OS_WIN
    // 仿YXPermission的GetMachineGUID逻辑
    QString guid = executeCommand("reg query \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography\" /v MachineGuid");

    // 解析注册表输出
    QRegularExpression regex("MachineGuid\\s+REG_SZ\\s+([A-Fa-f0-9\\-]+)");
    QRegularExpressionMatch match = regex.match(guid);

    if (match.hasMatch()) {
        return cleanString(match.captured(1));
    }
#endif

    return QString();
}

QString HardwareFingerprint::getWindowsBaseBoardUUID()
{
#ifdef Q_OS_WIN
    // 仿YXPermission的GetBaseBoardUuidByCmd逻辑
    QString uuid = executeCommand("wmic csproduct get uuid");

    // 解析wmic输出
    QStringList lines = uuid.split('\n');
    for (const QString &line : lines) {
        QString trimmed = line.trimmed();
        if (!trimmed.isEmpty() && trimmed != "UUID" &&
            !trimmed.contains("Not Available") &&
            !trimmed.contains("Not Specified")) {
            return cleanString(trimmed);
        }
    }
#endif

    return QString();
}
