#ifndef HARDWAREFINGERPRINT_H
#define HARDWAREFINGERPRINT_H

#include <QString>
#include <QCryptographicHash>
#include <QSysInfo>
#include <QNetworkInterface>
#include <QStorageInfo>
#include <QDir>
#include <QDebug>

/**
 * @brief 硬件指纹生成器（增强版）
 *
 * 用于生成设备唯一标识，基于多种硬件信息组合
 * 支持Linux和Windows平台，采用YXPermission的安全增强策略
 *
 * 主要改进：
 * - 增加加盐处理和多重哈希
 * - 支持SHA256加密算法
 * - 增强Windows平台兼容性
 * - 改进设备标识稳定性
 */
class HardwareFingerprint
{
public:
    /**
     * @brief 获取设备硬件指纹（增强版）
     * @param useSalt 是否使用加盐处理（默认true）
     * @param useMultiHash 是否使用多重哈希（默认false）
     * @return 32位或64位十六进制字符串
     */
    static QString getDeviceFingerprint(bool useSalt = true, bool useMultiHash = false);

    /**
     * @brief 获取设备许可证标识（仿YXPermission）
     * @param key 自定义密钥
     * @return SHA256哈希值
     */
    static QString getDeviceLicense(const QString &key);

    /**
     * @brief 获取主要设备标识（仿YXPermission的getDeviceNo）
     * @return 主设备标识
     */
    static QString getPrimaryDeviceId();

    /**
     * @brief 获取CPU信息
     * @return CPU序列号或型号信息
     */
    static QString getCpuInfo();

    /**
     * @brief 获取主板信息
     * @return 主板序列号或型号信息
     */
    static QString getMotherboardInfo();

    /**
     * @brief 获取MAC地址
     * @return 第一个有效网卡的MAC地址
     */
    static QString getMacAddress();

    /**
     * @brief 获取硬盘序列号
     * @return 系统盘序列号
     */
    static QString getDiskSerial();

    /**
     * @brief 获取系统信息
     * @return 系统版本和架构信息
     */
    static QString getSystemInfo();
    
    /**
     * @brief 验证设备指纹是否匹配（增强版）
     * @param storedFingerprint 存储的指纹
     * @param currentFingerprint 当前指纹
     * @param tolerance 容错级别（0-100，默认0表示完全匹配）
     * @return 是否匹配
     */
    static bool verifyFingerprint(const QString &storedFingerprint,
                                 const QString &currentFingerprint = QString(),
                                 int tolerance = 0);

    /**
     * @brief 获取Windows机器GUID（仿YXPermission）
     * @return Windows机器GUID
     */
    static QString getWindowsMachineGUID();

    /**
     * @brief 获取Windows主板UUID（仿YXPermission）
     * @return 主板UUID
     */
    static QString getWindowsBaseBoardUUID();

private:
    /**
     * @brief 执行系统命令并获取输出
     * @param command 命令
     * @return 命令输出
     */
    static QString executeCommand(const QString &command);

    /**
     * @brief 清理字符串（移除空白字符和特殊字符）
     * @param input 输入字符串
     * @return 清理后的字符串
     */
    static QString cleanString(const QString &input);

    /**
     * @brief 生成MD5哈希
     * @param input 输入字符串
     * @return MD5哈希值
     */
    static QString generateMD5(const QString &input);

    /**
     * @brief 生成SHA256哈希（新增）
     * @param input 输入字符串
     * @return SHA256哈希值
     */
    static QString generateSHA256(const QString &input);

    /**
     * @brief 生成加盐哈希（仿YXPermission）
     * @param input 输入字符串
     * @param salt 盐值
     * @param algorithm 哈希算法（MD5或SHA256）
     * @return 加盐哈希值
     */
    static QString generateSaltedHash(const QString &input, const QString &salt,
                                    QCryptographicHash::Algorithm algorithm = QCryptographicHash::Md5);

    /**
     * @brief 获取平台特定的设备信息
     * @return 平台特定信息
     */
    static QString getPlatformSpecificInfo();

    // 常量定义
    static const QString DEFAULT_SALT;
    static const QString LICENSE_SALT;
    static const QString DEVICE_PREFIX;
};

#endif // HARDWAREFINGERPRINT_H
